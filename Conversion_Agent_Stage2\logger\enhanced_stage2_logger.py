import logging, traceback
import os
import shutil
from typing import Optional
from config import Config


class EnhancedStage2Logger:
    """
    Simple Stage2 Logger for workflow logging.

    Features:
    - Object-based log file naming
    - Dual output control (console + file)
    - Automatic log file copying to QBook
    - Same directory structure as Excel files
    """
    
    def __init__(self, migration_name: str, schema_name: str, object_name: str,
                 objecttype: str, cloud_category: str = "local", dual_output: bool = True):
        """
        Initialize Stage2 logger.

        Args:
            migration_name: Migration identifier
            schema_name: Database schema name
            object_name: Database object name
            objecttype: Database object type
            cloud_category: "local" or "cloud"
            dual_output: True for console+file, False for file only
        """
        self.migration_name = migration_name
        self.schema_name = schema_name
        self.object_name = object_name
        self.objecttype = objecttype
        self.cloud_category = cloud_category.lower()
        self.dual_output = dual_output
        
        # Setup paths and logger
        self._setup_paths()
        self._setup_logger()
        self._clear_existing_log()

        # Log initialization
        self.info(f"🚀 Stage2 Logger initialized for {schema_name}.{object_name}")
    
    def _setup_paths(self):
        """Setup log file paths."""
        # Get base paths
        if self.cloud_category == "local":
            temp_root_path = Config.Temp_Local_Path
            qbook_root_path = Config.Qbook_Local_Path
        else:
            temp_root_path = Config.Temp_Path
            qbook_root_path = Config.Qbook_Path

        # Create directories
        self.temp_dir = os.path.join(temp_root_path, 'Stage2_Processing', self.migration_name,
                                   self.schema_name, self.objecttype, self.object_name)
        self.qbook_metadata_dir = os.path.join(qbook_root_path, 'Stage1_Metadata', self.migration_name,
                                             self.schema_name, self.objecttype, self.object_name)
        os.makedirs(self.temp_dir, exist_ok=True)

        # Log file paths
        log_filename = f"{self.schema_name}_{self.object_name}_Stage2.log"
        self.temp_log_path = os.path.join(self.temp_dir, log_filename)
        self.qbook_log_path = os.path.join(self.qbook_metadata_dir, log_filename)
    
    def _setup_logger(self):
        """Setup logger with appropriate handlers."""
        # Create unique logger name to avoid conflicts
        logger_name = f"stage2_{self.migration_name}_{self.schema_name}_{self.object_name}"
        self.logger = logging.getLogger(logger_name)
        
        # Clear any existing handlers
        self.logger.handlers.clear()
        
        # Set level
        self.logger.setLevel(logging.INFO)
        
        # Prevent propagation to avoid duplicate logs
        self.logger.propagate = False
        
        # File formatter (detailed)
        file_formatter = logging.Formatter(
            # "[ %(asctime)s ] %(levelname)s %(name)s (line:%(lineno)d) - \n%(message)s"
            "[ %(asctime)s ] %(levelname)s - \n%(message)s"
        )
        
        # Console formatter (simple - just the message)
        console_formatter = logging.Formatter("%(message)s")
        
        # File handler (always enabled)
        file_handler = logging.FileHandler(self.temp_log_path, mode='a', encoding='utf-8')
        file_handler.setFormatter(file_formatter)
        file_handler.setLevel(logging.INFO)
        self.logger.addHandler(file_handler)
        
        # Console handler (based on dual_output setting)
        if self.dual_output:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(console_formatter)
            console_handler.setLevel(logging.INFO)
            self.logger.addHandler(console_handler)
    
    def _clear_existing_log(self):
        """Clear existing log file if reprocessing same object."""
        if os.path.exists(self.temp_log_path):
            try:
                # Clear the file content
                open(self.temp_log_path, 'w').close()
            except Exception as e:
                print(f"⚠️ Warning: Could not clear existing log file: {str(e)}")
    
    def info(self, message: str):
        """Log INFO level message."""
        self.logger.info(message)
    
    def error(self, message: str):
        """Log ERROR level message."""
        self.logger.error(message)
    
    def warning(self, message: str):
        """Log WARNING level message."""
        self.logger.warning(message)
    
    def debug(self, message: str):
        """Log DEBUG level message."""
        self.logger.debug(message)
    
    def log_exception(self, exception: Exception, context: str = ""):
        """Log exception with full context."""
        if context:
            self.error(f"❌ Exception in {context}: {str(exception)}")
        else:
            self.error(f"❌ Exception: {str(exception)}")
        
        # Log full traceback to file only
        
        traceback_str = traceback.format_exc()
        
        # Create a file-only logger for traceback
        file_logger = logging.getLogger(f"{self.logger.name}_traceback")
        file_logger.handlers.clear()
        file_logger.propagate = False
        file_logger.setLevel(logging.ERROR)
        
        file_handler = logging.FileHandler(self.temp_log_path, mode='a', encoding='utf-8')
        file_handler.setFormatter(logging.Formatter("%(message)s"))
        file_logger.addHandler(file_handler)
        
        file_logger.error(f"Full traceback:\n{traceback_str}")
    
    def copy_to_qbook(self):
        """
        Copy completed log file to QBook path (same pattern as Excel files).
        """
        try:
            # Create QBook directory if it doesn't exist
            os.makedirs(self.qbook_metadata_dir, exist_ok=True)
            
            # Copy log file to QBook path
            if os.path.exists(self.temp_log_path):
                shutil.copy2(self.temp_log_path, self.qbook_log_path)
                self.info(f"✅ Log file copied to QBook path: {self.qbook_log_path}")
                return True
            else:
                self.error(f"❌ Temp log file not found: {self.temp_log_path}")
                return False
                
        except Exception as e:
            self.error(f"❌ Failed to copy log to QBook: {str(e)}")
            return False
    
    def cleanup_temp_log(self):
        """Clean up temporary log file (optional)."""
        try:
            if os.path.exists(self.temp_log_path):
                os.remove(self.temp_log_path)
                print(f"🗑️ Cleaned up temp log: {self.temp_log_path}")
        except Exception as e:
            print(f"⚠️ Warning: Could not clean up temp log: {str(e)}")
    
    def get_log_paths(self):
        """Get current log file paths."""
        return {
            "temp_log_path": self.temp_log_path,
            "qbook_log_path": self.qbook_log_path,
            "temp_dir": self.temp_dir,
            "qbook_metadata_dir": self.qbook_metadata_dir
        }


# Global logger instance (will be initialized by workflow)
stage2_logger: Optional[EnhancedStage2Logger] = None


def initialize_stage2_logger(migration_name: str, schema_name: str, object_name: str, 
                           objecttype: str, cloud_category: str = "local", 
                           dual_output: bool = True) -> EnhancedStage2Logger:
    """
    Initialize global Stage2 logger instance.
    
    Returns:
        EnhancedStage2Logger: Initialized logger instance
    """
    global stage2_logger
    stage2_logger = EnhancedStage2Logger(
        migration_name=migration_name,
        schema_name=schema_name,
        object_name=object_name,
        objecttype=objecttype,
        cloud_category=cloud_category,
        dual_output=dual_output
    )
    return stage2_logger


def log_info(message: str):
    """Convenience function for logging INFO messages."""
    if stage2_logger:
        stage2_logger.info(message)
    else:
        print(message)  # Fallback to print if logger not initialized


def log_error(message: str):
    """Convenience function for logging ERROR messages."""
    if stage2_logger:
        stage2_logger.error(message)
    else:
        print(message)  # Fallback to print if logger not initialized


def log_exception(exception: Exception, context: str = ""):
    """Convenience function for logging exceptions."""
    if stage2_logger:
        stage2_logger.log_exception(exception, context)
    else:
        print(f"❌ Exception in {context}: {str(exception)}")  # Fallback