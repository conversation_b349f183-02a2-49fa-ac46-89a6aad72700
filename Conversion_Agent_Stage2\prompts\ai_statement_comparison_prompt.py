"""
AI Statement Comparison Prompt for Stage 2 Processing.

This module creates prompts for AI-driven comparison of AI corrected output vs applied modules output
to determine if they have similar target functionality.
"""

from Conversion_Agent_Stage2.logger.enhanced_stage2_logger import log_info

def create_ai_statement_comparison_prompt(
    ai_corrected_statement: str,
    applied_modules_statement: str,
    db_terms: dict,
    responsible_modules_context: list = None
) -> str:
    """
    Create a module-aware prompt for AI-driven statement comparison.

    Args:
        ai_corrected_statement: AI corrected PostgreSQL statement from Stage 1
        applied_modules_statement: Statement after applying updated modules
        db_terms: Database-specific terminology
        responsible_modules_context: List of module info with responsibilities

    Returns:
        str: Module-aware comparison prompt for AI analysis
    """

    source_db = db_terms['source_db']  # Oracle
    target_db = db_terms['target_db']  # PostgreSQL
    expert_title = db_terms['expert_title']

    # Build module context section
    module_context_section = ""
    if responsible_modules_context:
        module_context_section = f"""
ENHANCED MODULES CONTEXT:
========================
The following modules were enhanced to produce the current pipeline output:

"""
        for i, module_info in enumerate(responsible_modules_context, 1):
            module_name = module_info.get('module_name', 'Unknown')
            responsibility = module_info.get('responsibility_reason', 'No responsibility provided')
            module_context_section += f"""
🎯 MODULE {i}: {module_name.upper()}
   Responsibility: {responsibility}
"""

        module_context_section += f"""
TOTAL ENHANCED MODULES: {len(responsible_modules_context)}

CRITICAL TRANSFORMATION GUIDANCE REQUIREMENTS:
- ALWAYS specify which module should handle each transformation in the transformation_method field
- Use format: "[module_name] module should handle [specific transformation]"
- For single module: Still specify the module name for clarity
- For multiple modules: Map each transformation to the appropriate module
- Example: "join module should handle ORDER BY clause reordering"
"""

        # Log the module context being passed to AI for visibility
        log_info(f"\n📋 MODULE CONTEXT BEING PASSED TO AI COMPARISON:")
        for i, module_info in enumerate(responsible_modules_context, 1):
            module_name = module_info.get('module_name', 'Unknown')
            responsibility = module_info.get('responsibility_reason', 'No responsibility provided')
            log_info(f"   🎯 Module {i}: {module_name}")
            log_info(f"      Path: {module_info.get('module_path', 'Unknown')}")
            log_info(f"      Full Responsibility: {responsibility}")
        log_info(f"📝 AI will receive this context to provide module-specific transformation guidance\n")

    prompt = f"""
You are a SENIOR {expert_title} with expertise in {source_db} to {target_db} conversion and {target_db} statement comparison.

PIPELINE CONVERSION CONTEXT:
===========================
**Conversion Direction**: {source_db} → {target_db}
**Source Database**: {source_db} (Original format)
**Target Database**: {target_db} (Expected final format)

Your task is to compare pipeline output against expected {target_db} output and provide conversion guidance.

PIPELINE STATE AWARENESS:
========================
The current pipeline output may contain MIXED conversion states:
- Some SQL constructs already converted to {target_db}
- Some SQL constructs still in {source_db} format
- Your task is to compare and provide guidance for completing the {source_db} → {target_db} conversion

COMPARISON CONTEXT:
==================
**Expected {target_db} Output** (Correct final format):
{ai_corrected_statement}

**Current Pipeline Output** (May contain mixed {source_db}/{target_db} syntax):
{applied_modules_statement}

{module_context_section}

CORE COMPARISON TASK:
====================
1. **CONVERSION COMPLETION**: Determine if pipeline output has completed {source_db} → {target_db} conversion
2. **SYNTAX MATCHING**: Check if the core SQL logic and structure match expected {target_db} format
3. **MIXED STATE RECOGNITION**: Identify any remaining {source_db} syntax that needs conversion
4. **LOGIC EQUIVALENCE**: Verify both statements implement the same database operations

COMPARISON CRITERIA:
===================

1. **CONVERSION STATE ANALYSIS**:
   - Identify {source_db} syntax patterns still present in pipeline output
   - Recognize {target_db} syntax patterns that are correctly converted
   - Determine what {source_db} constructs need conversion to {target_db}

2. **SQL CLAUSE ORDER VALIDATION**:
   - **CRITICAL**: SQL clause order must be identical for functional equivalence
   - All SQL clauses must appear in the same relative positions between statements
   - Clause positioning affects SQL execution behavior and results

3. **ELEMENTS TO IGNORE COMPLETELY** (CRITICAL - MUST BE IGNORED):
   - All whitespace, tabs, line breaks, and indentation differences
   - All case differences (uppercase vs lowercase)
   - All formatting and visual presentation variations
   - **ALL COMMENT CONTENT (-- comments, /* */ comments) - IGNORE COMPLETELY**
   - **ALL COMMENT MARKERS AND PROCESSING PLACEHOLDERS - IGNORE COMPLETELY**
   - **ALL TEMPORARY MARKERS USED FOR PROCESSING WORKFLOWS - IGNORE COMPLETELY**
   - Visual arrangement differences that don't affect SQL execution
   - Syntax formatting that doesn't change functional behavior
   - **IMPORTANT: Comments and markers have NO functional impact - treat as if they don't exist**

4. **CRITICAL DIFFERENCES TO IDENTIFY**:
   - Different SQL functions, operations, or keywords
   - Different data processing logic or algorithms
   - Missing or extra SQL functionality between statements
   - Different result sets or execution outcomes
   - Different positioning of SQL clauses that affects execution sequence
   - Extra or missing SQL statements or blocks
   - Structural differences that impact SQL execution behavior
   - Different clause ordering that changes functional behavior

COMPARISON METHODOLOGY:
======================

**STEP 1: NORMALIZE BOTH STATEMENTS**
- Convert both statements to consistent case (all uppercase or lowercase)
- Remove all whitespace variations (spaces, tabs, line breaks, indentation)
- Remove all comment content (lines starting with --, content within /* */)
- Remove comment marker patterns and processing placeholders
- Extract only the functional SQL tokens in their original sequence
- Result: Two clean token sequences for comparison

**STEP 2: TOKEN-BY-TOKEN COMPARISON**
- Compare the normalized token sequences element by element
- Identify tokens that are missing, extra, or in different positions
- Map each SQL keyword, function, and clause to its position in each statement
- Create a detailed difference list showing position mismatches
- Focus on SQL structure, not formatting differences

**STEP 3: SQL CLAUSE STRUCTURE ANALYSIS**
- Identify all SQL clauses in both statements (SELECT, FROM, WHERE, ORDER BY, LIMIT, etc.)
- Compare the sequential order of clauses between statements
- Check if clauses appear in the same relative positions
- Identify any clauses that are missing, extra, or repositioned
- Verify that clause ordering follows proper SQL execution sequence

**STEP 4: KEYWORD AND FUNCTION ANALYSIS**
- Compare SQL keywords and functions used in both statements
- Identify missing, extra, or different keywords that affect functionality
- Check for variations in SQL operations that change behavior
- Analyze function calls and their parameters for differences
- Focus on elements that impact SQL execution results

**STEP 5: COMPREHENSIVE DIFFERENCE REPORTING**
- List every structural difference found between normalized statements
- Specify exact SQL elements that differ in position or presence
- Explain how each difference impacts functional equivalence
- Categorize differences by their effect on SQL execution behavior
- Provide actionable feedback for addressing each difference

COMPARISON GUIDELINES:
=====================

**EQUIVALENT STATEMENTS** must have:
- Same core SQL operations and logic
- Same data processing approach
- **CRITICAL**: Identical element sequence and positioning
- **CRITICAL**: Same relative order of all components
- **NOTE**: Only whitespace, formatting, and case differences are ignored
- **CONVERSION COMPLETE**: All {source_db} syntax converted to {target_db}

**NON-EQUIVALENT STATEMENTS** have:
- Different SQL operations or functions
- Different data processing logic
- **CRITICAL**: Different element ordering or positioning
- **CRITICAL**: Different sequence of components (even if same components exist)
- Missing or extra functionality
- **CONVERSION INCOMPLETE**: {source_db} syntax still present in pipeline output

DECISION CRITERIA:
=================

**STATEMENTS MATCH** (return true):
✅ Both normalized statements implement identical SQL logic and operations
✅ Core functionality is equivalent after removing formatting differences
✅ Same data processing approach and result structure
✅ Identical sequential order of all SQL elements after normalization
✅ Same relative positioning of all functional components
✅ **CRITICAL: Only formatting, whitespace, case, and ALL COMMENTS differ between statements**
✅ **COMMENTS ARE NEVER A REASON FOR MISMATCH - IGNORE ALL COMMENT DIFFERENCES**
✅ **TRANSFORMATION GUIDANCE CRITERIA**:
  - Provide complete, implementable {source_db} → {target_db} transformation patterns
  - Include all syntax elements and parameters in patterns
  - Use exact syntax from the actual statements being compared
  - ALWAYS provide {source_db} → {target_db} conversion direction
  - NEVER suggest {target_db} → {source_db} reverse conversion
  - Ensure patterns are specific and actionable for code enhancement
❌ **NEVER use placeholders, ellipsis, or abbreviated syntax in patterns**
❌ **AVOID generic or vague pattern descriptions**

**STATEMENTS DON'T MATCH** (return false):
❌ Different SQL operations, functions, or keywords after normalization
❌ Missing or extra SQL functionality between normalized statements
❌ Different data processing approaches that yield different results
❌ Different positioning or ordering of SQL clauses that affects execution
❌ Structural differences that impact SQL execution behavior
❌ Different sequence of functional SQL components

OUTPUT FORMAT (JSON):
====================
{{
  "statements_match": <boolean - true if normalized statements are functionally equivalent, false if they have structural or functional differences>,
  "explanation": "<systematic analysis following this format: 1) List all SQL clause positioning differences, 2) List all missing or extra keywords/functions, 3) List all structural variations that affect execution, 4) Ignore all comments and formatting differences, 5) Provide specific actionable differences for module enhancement>",
  "transformation_guidance": {{
    "required": <boolean - true if transformation is needed>,
    "specific_changes": [
      {{
        "source_pattern": "<Complete current {source_db} or mixed pattern - NO placeholders or abbreviations>",
        "target_pattern": "<Complete target {target_db} pattern - NO placeholders or abbreviations>",
        "transformation_method": "<How to implement the {source_db} → {target_db} conversion - specify which module should handle this based on module context above>",
        "variations_to_handle": ["<variation1>", "<variation2>", "<variation3>"]
      }}
    ],
    "implementation_steps": [
      "<Step 1: Specific action - mention which module should handle this>",
      "<Step 2: Specific action - mention which module should handle this>",
      "<Step 3: Specific action - mention which module should handle this>"
    ],
    "parameter_mapping": {{
      "extraction": "<How to extract variable components from {source_db} pattern>",
      "reconstruction": "<How to reconstruct in {target_db} format>"
    }}
  }}
}}

**TRANSFORMATION PATTERN REQUIREMENTS**:
- **COMPLETE PATTERNS**: Always provide complete, executable {source_db} → {target_db} patterns - never use placeholders like (...), ..., or abbreviated syntax
- **FULL EXPRESSIONS**: Include complete expressions with all parameters and components
- **SPECIFIC SYNTAX**: Provide exact syntax that can be directly implemented without interpretation
- **NO ABBREVIATIONS**: Avoid shortened or truncated patterns - always show the complete structure
- **IMPLEMENTABLE GUIDANCE**: Ensure patterns can be directly used in {source_db} → {target_db} code transformations
- **PARAMETER COMPLETENESS**: Ensure all function parameters, expressions, and values are included
- **SYNTAX VALIDATION**: Verify each pattern is valid, executable syntax that won't cause implementation errors
- **CONVERSION DIRECTION**: Always ensure source_pattern represents {source_db} syntax and target_pattern represents {target_db} syntax

**PATTERN COMPLETENESS PRINCIPLES**:
❌ AVOID: Incomplete patterns with placeholders, ellipsis, or abbreviated syntax
✅ PROVIDE: Complete, specific patterns that include all necessary components and parameters
❌ AVOID: Generic or vague pattern descriptions
✅ PROVIDE: Exact syntax that matches the actual statement structure
❌ AVOID: Patterns with missing parameters, expressions, or syntax elements that would cause implementation errors
✅ PROVIDE: Patterns that can be directly copied and implemented without modification or interpretation

**PATTERN EXTRACTION AND VALIDATION**:
Before providing transformation guidance:
1. **EXTRACT COMPLETE EXPRESSIONS**: Identify the full expression or construct that needs transformation
2. **PRESERVE ALL COMPONENTS**: Include all parameters, functions, and syntax elements
3. **AVOID SHORTCUTS**: Never use placeholders, ellipsis, or abbreviated representations
4. **MAINTAIN SPECIFICITY**: Use exact syntax from the actual statements being compared
5. **ENSURE IMPLEMENTABILITY**: Verify patterns can be directly used without further interpretation
6. **VALIDATE SYNTAX**: Confirm each pattern is syntactically correct and executable
7. **CHECK COMPLETENESS**: Ensure all function parameters, expressions, and values are present and correct

**TRANSFORMATION GUIDANCE QUALITY STANDARDS**:
- **COMPLETE**: Every pattern includes all necessary syntax elements
- **SPECIFIC**: Patterns match the exact structure found in the statements
- **ACTIONABLE**: Patterns can be directly implemented in code transformations
- **UNAMBIGUOUS**: No interpretation required - patterns are explicit and clear
- **SYNTAX VERIFIED**: Each pattern is syntactically correct and ready for implementation
- **IMPLEMENTATION READY**: Patterns work when directly applied without modification

COMPARISON FOCUS:
================
- **Normalization First**: Always normalize both statements by removing whitespace/case differences and comments
- **Conversion Analysis**: Identify {source_db} syntax patterns that need conversion to {target_db}
- **Core Logic**: Compare normalized statements for same SQL operations
- **Clause Order**: Verify clause ordering in normalized versions
- **Functional Equivalence**: Focus on whether normalized statements would produce the same results
- **Transformation Direction**: Always provide {source_db} → {target_db} conversion guidance

NORMALIZATION EXAMPLES:
======================
Before normalization:
Statement A: "SELECT col1 FROM table WHERE id = 1 ORDER BY col1 LIMIT 5;"
Statement B: "select col1 from table where id = 1 limit 5 order by col1;"

After normalization (remove case/whitespace):
Statement A: "SELECT COL1 FROM TABLE WHERE ID = 1 ORDER BY COL1 LIMIT 5"
Statement B: "SELECT COL1 FROM TABLE WHERE ID = 1 LIMIT 5 ORDER BY COL1"

Analysis: Different clause positioning (ORDER BY vs LIMIT sequence) - NOT EQUIVALENT

Comment marker patterns to ignore during normalization:
- comment_quad_marker_<dynamic_number>_us
- All comment content and processing markers

ANALYSIS REQUIREMENTS:
=====================
1. **SYSTEMATIC NORMALIZATION**: Apply consistent normalization to both statements before comparison
2. **COMPREHENSIVE IDENTIFICATION**: Identify ALL structural and functional differences between normalized statements
3. **DETAILED EXPLANATION**: Provide specific details about each difference found and its impact on functionality
4. **PRECISE CATEGORIZATION**: Distinguish between ignorable formatting differences and critical functional differences
5. **COMPLETE COVERAGE**: Ensure no structural differences are missed in the analysis
6. **TRANSFORMATION GUIDANCE**: If statements don't match, provide specific actionable {source_db} → {target_db} guidance for enhancement:
   - Identify exact {source_db} patterns that need conversion to {target_db}
   - Specify target {target_db} patterns they should become
   - Provide implementation methods for the {source_db} → {target_db} transformation
   - Include variations that should be handled
   - Give step-by-step implementation guidance
   - Explain parameter extraction and reconstruction
   - NEVER provide {target_db} → {source_db} reverse conversion guidance
"""

    return prompt
