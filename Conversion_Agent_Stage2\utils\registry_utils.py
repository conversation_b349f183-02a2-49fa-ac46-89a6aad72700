"""
Registry utilities for Stage 2 enhanced module management.

This module provides functionality to:
- Check if enhanced modules exist in registry
- Load enhanced modules from registry
- Save enhanced modules to registry
- Manage registry paths and structure

Follows the same standards as conversion_nodes.py for path management and structure.
"""

import os
from typing import Optional
from config import Config
from Conversion_Agent_Stage2.logger.enhanced_stage2_logger import log_info, log_error


def get_registry_path(original_module_path: str, migration_name: str, cloud_category: str) -> str:
    """
    Convert original module path to registry path following existing code standards.

    Args:
        original_module_path: Original path like "Common/Statement/Pre/join.py"
        migration_name: Migration name for path construction
        cloud_category: Cloud category (local/cloud) - follows existing pattern

    Returns:
        Registry path for enhanced module

    Example:
        Input: "Common/Statement/Pre/join.py"
        Output: "qbookv2/Stage1_Metadata/Test_Migration/Enhanced_Modules_Registry/Common/Statement/Pre/join.py"
    """
    try:
        # Follow existing pattern from conversion_nodes.py
        if cloud_category.lower() == 'local':
            qbook_path = Config.Qbook_Local_Path
        else:
            qbook_path = Config.Qbook_Path

        # Construct registry base path following Stage1_Metadata pattern
        registry_base = os.path.join(qbook_path, "Stage1_Metadata", migration_name, "Enhanced_Modules_Registry")

        # Combine with original module path
        registry_path = os.path.join(registry_base, original_module_path)

        return registry_path.replace("\\", "/")

    except Exception as e:
        log_error(f"❌ Error constructing registry path: {str(e)}")
        return ""


def check_registry_for_module(module_path: str, migration_name: str, cloud_category: str) -> bool:
    """
    Check if enhanced version exists in registry.
    
    Args:
        module_path: Original module path
        migration_name: Migration name
        cloud_category: Cloud category
    
    Returns:
        True if enhanced module exists in registry, False otherwise
    """
    try:
        registry_path = get_registry_path(module_path, migration_name, cloud_category)
        
        if not registry_path:
            return False
            
        exists = os.path.exists(registry_path) and os.path.isfile(registry_path)
        
        if exists:
            print(f"✅ Enhanced module found in registry: {module_path}")
        else:
            print(f"📁 Enhanced module not in registry: {module_path}")
            
        return exists
        
    except Exception as e:
        print(f"❌ Error checking registry for module {module_path}: {str(e)}")
        return False


def load_enhanced_module_from_registry(module_path: str, migration_name: str, cloud_category: str) -> Optional[str]:
    """
    Load enhanced module from registry following existing patterns.

    Enhanced modules in registry are stored as plain text (already decrypted and enhanced).

    Args:
        module_path: Original module path
        migration_name: Migration name
        cloud_category: Cloud category (local/cloud)

    Returns:
        Enhanced module code if found, None otherwise
    """
    try:
        registry_path = get_registry_path(module_path, migration_name, cloud_category)

        if not registry_path or not os.path.exists(registry_path):
            log_info(f"⚠️ Enhanced module not found in registry: {module_path}")
            return None

        # Read enhanced module (stored as plain text, no decryption needed)
        with open(registry_path, 'r', encoding='utf-8') as file:
            enhanced_code = file.read()

        log_info(f"🗂️ Loaded enhanced module from registry: {module_path}")
        return enhanced_code

    except Exception as e:
        log_error(f"❌ Error loading enhanced module from registry {module_path}: {str(e)}")
        return None


def save_enhanced_module_to_registry(module_name: str, module_code: str, original_path: str,
                                   migration_name: str, cloud_category: str) -> bool:
    """
    Save enhanced module to registry following existing patterns.

    Enhanced modules are saved as plain text (no encryption needed since they're already processed).

    Args:
        module_name: Name of the module
        module_code: Enhanced module code (already decrypted and enhanced)
        original_path: Original module path
        migration_name: Migration name
        cloud_category: Cloud category (local/cloud)

    Returns:
        True if saved successfully, False otherwise
    """
    try:
        registry_path = get_registry_path(original_path, migration_name, cloud_category)

        if not registry_path:
            log_error(f"❌ Could not construct registry path for {module_name}")
            return False

        # Create directory structure if it doesn't exist (following existing pattern)
        registry_dir = os.path.dirname(registry_path)
        os.makedirs(registry_dir, exist_ok=True)

        # Save enhanced module as plain text (no encryption needed)
        with open(registry_path, 'w', encoding='utf-8') as file:
            file.write(module_code)

        log_info(f"💾 Saved enhanced module to registry: {module_name} → {registry_path}")
        return True

    except Exception as e:
        log_error(f"❌ Error saving enhanced module to registry {module_name}: {str(e)}")
        return False






