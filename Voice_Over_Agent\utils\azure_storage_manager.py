"""
Azure Storage Manager Module for Voice Over Agent

This module handles Azure Blob Storage operations including file uploads, container management,
and URL generation for the Voice Over Agent workflow.
"""

import os
from typing import Optional
from azure.storage.blob import BlobServiceClient
from config import Config


class AzureStorageManager:
    """
    Manages Azure Blob Storage operations for the Voice Over Agent.
    
    Features:
    - Automatic container creation and management
    - File upload with progress tracking
    - Blob URL generation for public access
    - Robust error handling and logging
    - Support for multiple container types (videos, audio, output)
    """
    
    def __init__(self):
        """
        Initialize the AzureStorageManager with Azure Blob Storage configuration.
        
        Raises:
            ValueError: If Azure Storage connection string is not configured
        """
        if not Config.AZURE_STORAGE_CONNECTION_STRING:
            raise ValueError("Azure Storage connection string not configured. Please update config.py")
        
        self.blob_service_client = BlobServiceClient.from_connection_string(
            Config.AZURE_STORAGE_CONNECTION_STRING
        )
        
        # Container names from configuration
        self.containers = {
            'videos': Config.AZURE_CONTAINER_VIDEOS,
            'audio': Config.AZURE_CONTAINER_AUDIO,
            'output': Config.AZURE_CONTAINER_OUTPUT
        }
        
        # Ensure all required containers exist
        self.ensure_containers_exist()
    
    def ensure_containers_exist(self) -> None:
        """
        Ensure all required blob containers exist, creating them if necessary.
        
        Creates containers for:
        - Videos: Input video files
        - Audio: Extracted and generated audio files
        - Output: Final processed video files
        """
        for container_type, container_name in self.containers.items():
            try:
                container_client = self.blob_service_client.get_container_client(container_name)
                if not container_client.exists():
                    container_client.create_container()
                    print(f"📦 Created {container_type} container: {container_name}")
                else:
                    print(f"✅ {container_type.capitalize()} container exists: {container_name}")
            except Exception as e:
                print(f"⚠️ Could not create {container_type} container {container_name}: {str(e)}")
    
    def upload_file(self, file_path: str, container_name: str, blob_name: Optional[str] = None) -> Optional[str]:
        """
        Upload file to Azure Blob Storage and return the blob URL.
        
        Args:
            file_path (str): Local path to the file to upload
            container_name (str): Name of the Azure Blob container
            blob_name (Optional[str]): Name for the blob (defaults to filename)
            
        Returns:
            Optional[str]: Blob URL if upload successful, None otherwise
            
        Features:
        - Automatic blob naming from filename if not specified
        - Overwrite existing blobs with same name
        - Detailed upload progress logging
        - File size validation and reporting
        """
        try:
            # Validate input file
            if not self._validate_file(file_path):
                return None
            
            # Generate blob name if not provided
            if blob_name is None:
                blob_name = os.path.basename(file_path)

            file_size = os.path.getsize(file_path)
            print(f"☁️ Uploading {file_path} to Azure Blob Storage...")
            print(f"📊 File size: {file_size / (1024*1024):.2f} MB")
            print(f"📦 Container: {container_name}")
            print(f"📄 Blob name: {blob_name}")

            # Get blob client and upload file
            blob_client = self.blob_service_client.get_blob_client(
                container=container_name,
                blob=blob_name
            )

            with open(file_path, 'rb') as data:
                blob_client.upload_blob(data, overwrite=True)

            blob_url = blob_client.url
            print(f"✅ File uploaded successfully to: {blob_url}")
            return blob_url

        except Exception as e:
            print(f"❌ Error uploading to Azure Blob Storage: {str(e)}")
            return None
    
    def upload_output_video(self, file_path: str, blob_name: Optional[str] = None) -> Optional[str]:
        """
        Upload final processed video to the output container.
        
        Args:
            file_path (str): Local path to the processed video file
            blob_name (Optional[str]): Name for the blob (defaults to filename)
            
        Returns:
            Optional[str]: Blob URL if upload successful, None otherwise
        """
        return self.upload_file(file_path, self.containers['output'], blob_name)
    
    def upload_audio_file(self, file_path: str, blob_name: Optional[str] = None) -> Optional[str]:
        """
        Upload audio file to the audio container.
        
        Args:
            file_path (str): Local path to the audio file
            blob_name (Optional[str]): Name for the blob (defaults to filename)
            
        Returns:
            Optional[str]: Blob URL if upload successful, None otherwise
        """
        return self.upload_file(file_path, self.containers['audio'], blob_name)
    
    def upload_video_file(self, file_path: str, blob_name: Optional[str] = None) -> Optional[str]:
        """
        Upload video file to the videos container.
        
        Args:
            file_path (str): Local path to the video file
            blob_name (Optional[str]): Name for the blob (defaults to filename)
            
        Returns:
            Optional[str]: Blob URL if upload successful, None otherwise
        """
        return self.upload_file(file_path, self.containers['videos'], blob_name)
    
    def _validate_file(self, file_path: str) -> bool:
        """
        Validate file exists and is not empty before upload.
        
        Args:
            file_path (str): Path to the file to validate
            
        Returns:
            bool: True if file is valid, False otherwise
        """
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            return False
        
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            print(f"❌ File is empty: {file_path}")
            return False
        
        return True
    
    def get_container_info(self) -> dict:
        """
        Get information about configured containers.
        
        Returns:
            dict: Container configuration information
        """
        return self.containers.copy()
    
    def delete_blob(self, container_name: str, blob_name: str) -> bool:
        """
        Delete a blob from Azure Storage.
        
        Args:
            container_name (str): Name of the container
            blob_name (str): Name of the blob to delete
            
        Returns:
            bool: True if deletion successful, False otherwise
        """
        try:
            blob_client = self.blob_service_client.get_blob_client(
                container=container_name,
                blob=blob_name
            )
            blob_client.delete_blob()
            print(f"🗑️ Deleted blob: {blob_name} from container: {container_name}")
            return True
        except Exception as e:
            print(f"❌ Error deleting blob {blob_name}: {str(e)}")
            return False