"""
Speech Synthesizer Module for Voice Over Agent

This module handles AI voice generation using Azure Speech Services with support for
multiple voice options and high-quality audio synthesis.
"""

from typing import Optional
import azure.cognitiveservices.speech as speechsdk
from config import Config


class SpeechSynthesizer:
    """
    Handles AI voice generation using Azure Speech Services.
    
    Features:
    - Multiple Azure neural voice options
    - High-quality speech synthesis
    - Voice name mapping and validation
    - Robust error handling and logging
    - Configurable output formats
    """
    
    def __init__(self, speech_config: speechsdk.SpeechConfig):
        """
        Initialize the SpeechSynthesizer with Azure Speech configuration.
        
        Args:
            speech_config (speechsdk.SpeechConfig): Configured Azure Speech Services client
        """
        self.speech_config = speech_config
        self.default_voice = Config.AZURE_SPEECH_TTS_CONFIG.get("voice_name", "en-US-AriaNeural")
        self.voice_options = getattr(Config, 'AZURE_VOICE_OPTIONS', {})
    
    def generate_ai_voice(self, text: str, output_path: str, voice_name: Optional[str] = None) -> bool:
        """
        Generate AI voice from text using Azure Speech Services.
        
        Args:
            text (str): Text to convert to speech
            output_path (str): Path where audio file will be saved
            voice_name (Optional[str]): Azure neural voice name to use
            
        Returns:
            bool: True if synthesis successful, False otherwise
            
        Features:
        - Automatic voice name mapping from short names to full Azure voice names
        - High-quality neural voice synthesis
        - Detailed logging and progress tracking
        - Comprehensive error handling
        """
        try:
            # Determine voice to use
            selected_voice = self._resolve_voice_name(voice_name)
            
            print("🔧 Generating AI voice with Azure Speech Services...")
            print(f"📊 Text length: {len(text)} characters")
            print(f"🎤 Voice: {selected_voice}")

            # Configure speech synthesis voice
            self.speech_config.speech_synthesis_voice_name = selected_voice

            # Create audio configuration for file output
            audio_config = speechsdk.audio.AudioOutputConfig(filename=output_path)

            # Create speech synthesizer
            speech_synthesizer = speechsdk.SpeechSynthesizer(
                speech_config=self.speech_config,
                audio_config=audio_config
            )

            # Generate speech
            print("🔧 Synthesizing speech...")
            result = speech_synthesizer.speak_text_async(text).get()

            # Handle synthesis result
            return self._handle_synthesis_result(result, output_path)

        except Exception as e:
            print(f"❌ Error generating AI voice: {str(e)}")
            return False
    
    def _resolve_voice_name(self, voice_name: Optional[str]) -> str:
        """
        Resolve voice name from short name to full Azure voice name.
        
        Args:
            voice_name (Optional[str]): Voice name (short or full)
            
        Returns:
            str: Full Azure voice name
        """
        if voice_name is None:
            return self.default_voice
        
        # Check if it's a short name that needs mapping
        if voice_name in self.voice_options:
            full_voice_name = self.voice_options[voice_name]
            print(f"🔧 Mapping voice '{voice_name}' to '{full_voice_name}'")
            return full_voice_name
        
        # Return as-is if it's already a full voice name
        return voice_name
    
    def _handle_synthesis_result(self, result: speechsdk.SpeechSynthesisResult, output_path: str) -> bool:
        """
        Handle the result of speech synthesis operation.
        
        Args:
            result (speechsdk.SpeechSynthesisResult): Synthesis result from Azure
            output_path (str): Path where audio was saved
            
        Returns:
            bool: True if synthesis was successful, False otherwise
        """
        if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
            print(f"✅ AI voice generated successfully: {output_path}")
            return True
        elif result.reason == speechsdk.ResultReason.Canceled:
            cancellation_details = result.cancellation_details
            print(f"❌ Speech synthesis canceled: {cancellation_details.reason}")
            if cancellation_details.error_details:
                print(f"❌ Error details: {cancellation_details.error_details}")
            return False
        else:
            print(f"❌ Speech synthesis failed: {result.reason}")
            return False
    
    def get_available_voices(self) -> dict:
        """
        Get available voice options configured for the system.
        
        Returns:
            dict: Dictionary of available voice mappings
        """
        return self.voice_options.copy()
    
    def validate_voice_name(self, voice_name: str) -> bool:
        """
        Validate if a voice name is available in the system.
        
        Args:
            voice_name (str): Voice name to validate
            
        Returns:
            bool: True if voice is available, False otherwise
        """
        return voice_name in self.voice_options or voice_name == self.default_voice