"""
Video Downloader Module for Voice Over Agent

This module handles downloading videos from URLs with progress tracking and validation.
Supports streaming downloads with progress bars and robust error handling.
"""

import os
import requests
from tqdm import tqdm
from typing import Optional
from config import Config


class VideoDownloader:
    """
    Handles video downloading from URLs with progress tracking and validation.
    
    Features:
    - Streaming downloads with progress bars
    - Content-length validation
    - Configurable chunk size
    - Robust error handling and logging
    """
    
    def __init__(self):
        """Initialize the VideoDownloader with default configuration."""
        self.chunk_size = getattr(Config, 'VOICE_CHUNK_SIZE', 8192)
    
    def download_video(self, video_url: str, output_path: str) -> bool:
        """
        Download video from URL to local path with progress tracking.
        
        Args:
            video_url (str): URL of the video to download
            output_path (str): Local path where video will be saved
            
        Returns:
            bool: True if download successful, False otherwise
            
        Raises:
            requests.RequestException: If HTTP request fails
            IOError: If file writing fails
        """
        try:
            print(f"📥 Downloading video from: {video_url}")
            
            # Make HTTP request with streaming enabled
            response = requests.get(video_url, stream=True)
            response.raise_for_status()

            # Get total file size for progress tracking
            total_size = int(response.headers.get('content-length', 0))
            
            if total_size == 0:
                print("⚠️ Warning: Content-length not provided by server")

            # Download with progress bar
            with open(output_path, 'wb') as file, tqdm(
                desc="Downloading Video",
                total=total_size,
                unit='B',
                unit_scale=True,
                unit_divisor=1024,
            ) as pbar:
                for chunk in response.iter_content(chunk_size=self.chunk_size):
                    if chunk:
                        file.write(chunk)
                        pbar.update(len(chunk))

            # Validate downloaded file
            if not self._validate_downloaded_file(output_path):
                return False

            print(f"✅ Video downloaded successfully: {output_path}")
            file_size = os.path.getsize(output_path)
            print(f"📊 Downloaded file size: {file_size / (1024*1024):.2f} MB")
            return True

        except requests.RequestException as e:
            print(f"❌ HTTP error downloading video: {str(e)}")
            return False
        except IOError as e:
            print(f"❌ File I/O error downloading video: {str(e)}")
            return False
        except Exception as e:
            print(f"❌ Unexpected error downloading video: {str(e)}")
            return False
    
    def _validate_downloaded_file(self, file_path: str) -> bool:
        """
        Validate that the downloaded file exists and is not empty.
        
        Args:
            file_path (str): Path to the downloaded file
            
        Returns:
            bool: True if file is valid, False otherwise
        """
        if not os.path.exists(file_path):
            print(f"❌ Downloaded file not found: {file_path}")
            return False
            
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            print(f"❌ Downloaded file is empty: {file_path}")
            return False
            
        return True