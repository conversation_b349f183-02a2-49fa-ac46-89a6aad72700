"""
Local Main for Voice Over Agent

This script runs the Voice Over agent locally with hardcoded values,
similar to other local_main_*.py files but for voice over conversion.
"""

import sys
import argparse
from typing import Any
from Voice_Over_Agent.state.state import VoiceOverRequest
from Voice_Over_Agent.utils.voice_processor import process_voice_over_request
from config import Config


def print_banner():
    """Print application banner"""
    print("🎬 Azure AI Voice-Over Conversion Tool")
    print("=" * 50)


def print_voice_options():
    """Print available voice options"""
    print("\n🎤 Available Azure Neural Voices:")
    for key, voice_name in Config.AZURE_VOICE_OPTIONS.items():
        print(f"  {key}: {voice_name}")
    print()


def setup_application() -> Any:
    """
    Set up the Voice Over Agent application.

    This function handles the complete application initialization process for voice over
    conversion workflows. It validates the configuration and prepares the system for
    Azure-based voice conversion operations.

    The setup process includes:
        - Loading configuration from Voice_Over_Agent.config
        - Validating Azure credentials and services
        - Preparing the system for voice conversion operations

    Returns:
        Any: Application setup status (currently returns True for success)

    Raises:
        Exception: If configuration validation fails or Azure services are unavailable
    """
    try:
        print("🔧 Setting up Voice Over Agent...")
        
        # Basic validation using root config
        if not Config.AZURE_SPEECH_KEY or Config.AZURE_SPEECH_KEY == "your_azure_speech_key_here":
            raise ValueError("Azure Speech key not configured. Please update config.py")

        if not Config.AZURE_SPEECH_REGION:
            raise ValueError("Azure Speech region not configured. Please update config.py")

        if not Config.AZURE_STORAGE_CONNECTION_STRING:
            raise ValueError("Azure Storage connection string not configured. Please update config.py")
        
        print("✅ Voice Over Agent setup completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error setting up Voice Over Agent: {str(e)}")
        raise


def run_voice_over_conversion(video_url: str, voice_name: str = "aria") -> None:
    """
    Run voice over conversion with the provided parameters.

    Args:
        video_url: URL of the video to process
        voice_name: Azure neural voice to use (default: aria)
    """
    try:
        print(f"🎬 Starting voice over conversion...")
        print(f"📹 Video URL: {video_url}")
        print(f"🎤 Voice: {Config.AZURE_VOICE_OPTIONS.get(voice_name, voice_name)}")
        print("=" * 50)

        # Create request
        request = VoiceOverRequest(
            video_url=video_url,
            voice_name=voice_name
        )

        # Process the request
        response = process_voice_over_request(
            video_url=request.video_url,
            voice_name=request.voice_name
        )

        # Display results
        if response.success:
            print("\n✅ SUCCESS!")
            print(f"📁 Output: {response.output_url}")
            print(f"⏱️ Processing time: {response.processing_time:.2f} seconds")
            print("🌐 Video processed with Azure AI voice")
        else:
            print("\n❌ FAILED!")
            print(f"Error: {response.error_message}")
            if response.processing_time:
                print(f"⏱️ Processing time: {response.processing_time:.2f} seconds")
            print("Check the logs for more details.")
            sys.exit(1)

    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        sys.exit(1)


def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(
        description="Convert human voice to AI voice in videos using Azure services",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python local_main_voice_over.py "https://example.com/video.mp4"
  python local_main_voice_over.py "https://example.com/video.mp4" --voice aria
  python local_main_voice_over.py "https://example.com/video.mp4" --voice jenny
  python local_main_voice_over.py --list-voices
        """
    )
    
    parser.add_argument(
        "video_url", 
        nargs='?',
        help="URL of the video to process"
    )
    
    parser.add_argument(
        "--voice", 
        choices=list(Config.AZURE_VOICE_OPTIONS.keys()),
        default="aria",
        help="Azure neural voice to use (default: aria)"
    )
    
    parser.add_argument(
        "--list-voices",
        action="store_true",
        help="List available Azure neural voices"
    )
    
    args = parser.parse_args()
    
    print_banner()
    
    if args.list_voices:
        print_voice_options()
        return
    
    if not args.video_url:
        print("❌ Error: Video URL is required")
        parser.print_help()
        sys.exit(1)
    
    try:
        # Setup application
        setup_application()
        
        # Run voice over conversion
        run_voice_over_conversion(args.video_url, args.voice)
        
    except KeyboardInterrupt:
        print("\n⚠️ Process interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
